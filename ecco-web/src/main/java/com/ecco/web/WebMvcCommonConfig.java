package com.ecco.web;

import com.ecco.security.ReferenceDataSource;
import com.ecco.web.hbs.FormatHelpers;
import com.ecco.web.nav.CommonOnlinePageController;
import com.github.jknack.handlebars.cache.HighConcurrencyTemplateCache;
import com.github.jknack.handlebars.helper.ConditionalHelpers;
import com.github.jknack.handlebars.helper.StringHelpers;
import com.github.jknack.handlebars.springmvc.HandlebarsViewResolver;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScan.Filter;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.WebAttributes;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.ui.ModelMap;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.handler.SimpleUrlHandlerMapping;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

/**
 * Extracted from WebMvcConfig so that it can be used in both ecco-war and ecco-webapi-boot.
 */
@Configuration
@ComponentScan(
        basePackageClasses = {CommonOnlinePageController.class, PublicPageController.class},
        includeFilters = @Filter(Controller.class)
)
@EnableTransactionManagement(proxyTargetClass=true)
public class WebMvcCommonConfig implements WebMvcConfigurer {

    @Value("${ecco.mvc.basePath:}")
    private String mvcBasePath;

    @Bean
    public HandlebarsViewResolver handlebarsViewResolver() {
        var resolver = new HandlebarsViewResolver();
        resolver.setOrder(3);
        resolver.setFailOnMissingFile(false);
        resolver.setPrefix("classpath:handlebars/");
        resolver.setSuffix(".hbs");
        resolver.setExposeRequestAttributes(true);

        // FIXME: This doesn't work because while the wrapped request exposes the beans as attributes, they don't get
        //   added to the model beforehand. Set breakpoint in ContextExposingHttpServletRequest.getAttribute to see.
        //        resolver.setExposeContextBeansAsAttributes(true);
        //        resolver.setExposedContextBeanNames("applicationProperties", "settingsService", "softwareFeatureService");

        resolver.setCache(false); // FIXME: This disabled templateCache because reload=true doesn't seem to work
        var templateCache = new HighConcurrencyTemplateCache();
        templateCache.setReload(true);
        resolver.setTemplateCache(templateCache);

        // {{message code}} is registered by default

        resolver.registerHelper("and", ConditionalHelpers.and);
        resolver.registerHelper("or", ConditionalHelpers.or);

        resolver.registerHelper("join", StringHelpers.join);
        resolver.registerHelper("userFriendlyDateFromLocal_usingJodaDateTime", FormatHelpers.userFriendlyDateFromLocal_usingJodaDateTime);
        resolver.registerHelper("userFriendlyDateFromLocal_usingMed", FormatHelpers.userFriendlyDateFromLocal_usingMed);
        resolver.registerHelper("localDate", FormatHelpers.localDate);
        resolver.registerHelper("spaceToQuotedArray", FormatHelpers.spaceToQuotedArray);

        return resolver;
    }

    @Bean
    public SimpleUrlHandlerMapping loginHbsView(@Qualifier("applicationReferenceData") ReferenceDataSource referenceData) {
        var controller = new BasicPageController("login", referenceData) {
            @Override
            protected void decorateModel(ModelMap model, HttpServletRequest request) {
                model.addAllAttributes(request.getParameterMap());
                boolean notAllowedError = request.getSession().getAttribute(WebAttributes.AUTHENTICATION_EXCEPTION) instanceof AuthenticationServiceException;
                if (notAllowedError) {
                    getMessageSourceAccessor().getMessage("checkedErrorUnhandled.message");
                } else {
                    model.addAttribute("errorMessage",
                            request.getSession().getAttribute(WebAttributes.AUTHENTICATION_EXCEPTION) == null
                                    ? "login failed (tech: no session?)"
                                    : ((AuthenticationException) request.getSession().getAttribute(WebAttributes.AUTHENTICATION_EXCEPTION)).getMessage()
                    );
                }
            }
        };
        return getHandlerMapping(controller, "/secure/login.html");
    }

    @NonNull
    private SimpleUrlHandlerMapping getHandlerMapping(BasicPageController controller, String... paths) {
        SimpleUrlHandlerMapping mapping = new SimpleUrlHandlerMapping();
        mapping.setOrder(Integer.MAX_VALUE - 2);
        var mappings = new HashMap<String, BasicPageController>();
        for (String path : paths) {
            // ecco.mvc.basePath defaults to "" but in webapi-boot we add /nav to match where it was deployed on servlet path
            mappings.put(mvcBasePath + path, controller);
        }
        mapping.setUrlMap(mappings);
        return mapping;
    }
}
